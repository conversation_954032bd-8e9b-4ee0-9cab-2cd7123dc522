import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// GET /api/purchase-orders/[id]/print - Generate PDF for purchase order
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get purchase order with all details
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        supplier: true,
        createdBy: {
          select: { id: true, name: true, email: true },
        },
        approvedBy: {
          select: { id: true, name: true, email: true },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Get store information
    const storeInfo = await prisma.storeInfo.findFirst({
      where: { id: "default-store" }
    });

    // Generate PDF
    const pdfBytes = await generatePurchaseOrderPDF(purchaseOrder, storeInfo);

    return new NextResponse(pdfBytes, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="purchase-order-${purchaseOrder.id.slice(-8).toUpperCase()}.pdf"`,
      },
    });
  } catch (error) {
    console.error('Error generating purchase order PDF:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function generatePurchaseOrderPDF(purchaseOrder: any, storeInfo: any) {
  // Create a new PDF document
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([595, 842]); // A4 portrait

  // Embed fonts
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  const { width, height } = page.getSize();
  const margin = 50;
  let y = height - margin;

  // Helper function to draw text
  const drawText = (text: string, x: number, yPos: number, options: any = {}) => {
    page.drawText(text, {
      x,
      y: yPos,
      size: options.size || 10,
      font: options.bold ? boldFont : font,
      color: options.color || rgb(0, 0, 0),
      maxWidth: options.maxWidth,
    });
  };

  // Header
  drawText('PURCHASE ORDER', margin, y, { size: 20, bold: true });
  y -= 30;

  // Store information (if available)
  if (storeInfo) {
    drawText(storeInfo.storeName || 'Next POS Store', margin, y, { size: 14, bold: true });
    y -= 15;
    if (storeInfo.address) {
      drawText(storeInfo.address, margin, y, { size: 10 });
      y -= 12;
    }
    if (storeInfo.phone) {
      drawText(`Phone: ${storeInfo.phone}`, margin, y, { size: 10 });
      y -= 12;
    }
    if (storeInfo.email) {
      drawText(`Email: ${storeInfo.email}`, margin, y, { size: 10 });
      y -= 12;
    }
  }

  y -= 20;

  // Purchase Order Information
  const poNumber = purchaseOrder.id.slice(-8).toUpperCase();
  drawText(`PO Number: ${poNumber}`, margin, y, { size: 12, bold: true });
  drawText(`Date: ${new Date(purchaseOrder.orderDate).toLocaleDateString()}`, width - margin - 150, y, { size: 12 });
  y -= 15;

  drawText(`Status: ${purchaseOrder.status}`, margin, y, { size: 10 });
  drawText(`Created by: ${purchaseOrder.createdBy.name}`, width - margin - 150, y, { size: 10 });
  y -= 20;

  // Supplier Information
  drawText('SUPPLIER INFORMATION', margin, y, { size: 12, bold: true });
  y -= 15;
  
  drawText(`Name: ${purchaseOrder.supplier.name}`, margin, y, { size: 10 });
  y -= 12;
  
  if (purchaseOrder.supplier.contactPerson) {
    drawText(`Contact: ${purchaseOrder.supplier.contactPerson}`, margin, y, { size: 10 });
    y -= 12;
  }
  
  if (purchaseOrder.supplier.phone) {
    drawText(`Phone: ${purchaseOrder.supplier.phone}`, margin, y, { size: 10 });
    y -= 12;
  }
  
  if (purchaseOrder.supplier.email) {
    drawText(`Email: ${purchaseOrder.supplier.email}`, margin, y, { size: 10 });
    y -= 12;
  }
  
  if (purchaseOrder.supplier.address) {
    drawText(`Address: ${purchaseOrder.supplier.address}`, margin, y, { size: 10, maxWidth: width - margin * 2 });
    y -= 12;
  }

  y -= 20;

  // Items table header
  drawText('ITEMS', margin, y, { size: 12, bold: true });
  y -= 20;

  // Table headers
  const tableY = y;
  page.drawRectangle({
    x: margin,
    y: tableY - 15,
    width: width - margin * 2,
    height: 15,
    color: rgb(0.9, 0.9, 0.9),
  });

  drawText('Product', margin + 5, tableY - 10, { size: 9, bold: true });
  drawText('SKU', margin + 150, tableY - 10, { size: 9, bold: true });
  drawText('Qty', margin + 220, tableY - 10, { size: 9, bold: true });
  drawText('Unit Price', margin + 260, tableY - 10, { size: 9, bold: true });
  drawText('Subtotal', margin + 350, tableY - 10, { size: 9, bold: true });

  y = tableY - 25;

  // Items
  for (const item of purchaseOrder.items) {
    if (y < margin + 100) {
      // Add new page if needed
      const newPage = pdfDoc.addPage([595, 842]);
      page = newPage;
      y = height - margin;
    }

    drawText(item.product.name, margin + 5, y, { size: 9, maxWidth: 140 });
    drawText(item.product.sku, margin + 150, y, { size: 9 });
    drawText(Number(item.quantity).toString(), margin + 220, y, { size: 9 });
    drawText(`Rp ${Number(item.unitPrice).toLocaleString('id-ID')}`, margin + 260, y, { size: 9 });
    drawText(`Rp ${Number(item.subtotal).toLocaleString('id-ID')}`, margin + 350, y, { size: 9 });

    y -= 15;
  }

  y -= 10;

  // Totals
  const totalsX = width - margin - 200;
  drawText('Subtotal:', totalsX, y, { size: 10, bold: true });
  drawText(`Rp ${Number(purchaseOrder.subtotal).toLocaleString('id-ID')}`, totalsX + 80, y, { size: 10 });
  y -= 15;

  const taxLabel = purchaseOrder.taxPercentage 
    ? `Tax (${purchaseOrder.taxPercentage}%):`
    : 'Tax:';
  drawText(taxLabel, totalsX, y, { size: 10, bold: true });
  drawText(`Rp ${Number(purchaseOrder.tax).toLocaleString('id-ID')}`, totalsX + 80, y, { size: 10 });
  y -= 15;

  // Draw line above total
  page.drawLine({
    start: { x: totalsX, y: y + 5 },
    end: { x: totalsX + 150, y: y + 5 },
    thickness: 1,
    color: rgb(0, 0, 0),
  });

  drawText('TOTAL:', totalsX, y, { size: 12, bold: true });
  drawText(`Rp ${Number(purchaseOrder.total).toLocaleString('id-ID')}`, totalsX + 80, y, { size: 12, bold: true });
  y -= 30;

  // Notes
  if (purchaseOrder.notes) {
    drawText('NOTES:', margin, y, { size: 10, bold: true });
    y -= 15;
    drawText(purchaseOrder.notes, margin, y, { size: 10, maxWidth: width - margin * 2 });
    y -= 20;
  }

  // Footer
  y = margin + 50;
  drawText('Authorized Signature: ____________________', margin, y, { size: 10 });
  drawText('Date: ____________________', width - margin - 150, y, { size: 10 });

  // Footer with generation info
  drawText(`Generated on ${new Date().toLocaleString()}`, margin, 20, { size: 8, color: rgb(0.5, 0.5, 0.5) });

  return await pdfDoc.save();
}
