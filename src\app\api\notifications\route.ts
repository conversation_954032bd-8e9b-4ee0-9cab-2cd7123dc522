import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// GET /api/notifications - Get all notifications for the current user
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const unreadOnly = searchParams.get("unread") === "true";
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const page = parseInt(searchParams.get("page") || "1", 10);
    const skip = (page - 1) * limit;

    // Build the query
    const where = {
      userId: auth.user.id,
      ...(unreadOnly ? { isRead: false } : {}),
    };

    // Get notifications count for pagination
    const totalCount = await prisma.notification.count({ where });

    // Get notifications
    const notifications = await prisma.notification.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });

    return NextResponse.json({
      notifications,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error getting notifications:", error);
    return NextResponse.json(
      { error: "Failed to get notifications", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/notifications - Create a new notification
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only allow SUPER_ADMIN to create notifications for other users
    const isSuperAdmin = auth.user.role === "SUPER_ADMIN";

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.message) {
      return NextResponse.json(
        { error: "Title and message are required" },
        { status: 400 }
      );
    }

    // If userId is provided, only super admins can create notifications for other users
    if (body.userId && body.userId !== auth.user.id && !isSuperAdmin) {
      return NextResponse.json(
        { error: "You don't have permission to create notifications for other users" },
        { status: 403 }
      );
    }

    // Create notification
    const notification = await prisma.notification.create({
      data: {
        userId: body.userId || auth.user.id,
        title: body.title,
        message: body.message,
        type: body.type || "SYSTEM",
      },
    });

    return NextResponse.json({ notification }, { status: 201 });
  } catch (error) {
    console.error("Error creating notification:", error);
    return NextResponse.json(
      { error: "Failed to create notification", message: (error as Error).message },
      { status: 500 }
    );
  }
}
