"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Boxes,
  ClipboardList,
  Package,
  PackageOpen,
  Truck,
  ArrowRightCircle,
  BarChart3,
  ShoppingCart,
} from "lucide-react";
import Link from "next/link";

export default function InventoryLandingPage() {
  const inventoryFeatures = [
    {
      title: "Products",
      description: "Manage your product catalog, categories, and pricing",
      icon: <Package className="h-8 w-8 text-primary" />,
      link: "/inventory/products",
      actions: [
        { label: "View Products", link: "/inventory/products" },
        { label: "Add New Product", link: "/inventory/products/new" },
        { label: "Manage Categories", link: "/inventory/categories" },
      ],
    },
    {
      title: "Stock Management",
      description: "Track inventory levels, adjustments, and transfers",
      icon: <Boxes className="h-8 w-8 text-primary" />,
      link: "/inventory/stock",
      actions: [
        { label: "View Stock", link: "/inventory/stock" },
        { label: "Make Adjustment", link: "/inventory/stock/adjustments" },
        { label: "Transfer Stock", link: "/inventory/stock/simple-transfers" },
      ],
    },
    {
      title: "Inventory Reports",
      description: "Generate and export inventory reports",
      icon: <ClipboardList className="h-8 w-8 text-primary" />,
      link: "/inventory/reports",
      actions: [
        { label: "Summary Report", link: "/inventory/reports?tab=summary" },
        { label: "Valuation Report", link: "/inventory/reports?tab=valuation" },
        { label: "Movement Report", link: "/inventory/reports?tab=movement" },
      ],
    },
    {
      title: "Suppliers",
      description: "Manage suppliers and purchase orders",
      icon: <Truck className="h-8 w-8 text-primary" />,
      link: "/inventory/suppliers",
      actions: [
        { label: "View Suppliers", link: "/inventory/suppliers" },
        { label: "Add New Supplier", link: "/inventory/suppliers/new" },
        { label: "Purchase Orders", link: "/inventory/purchase-orders" },
      ],
    },
    {
      title: "Point of Sale",
      description: "Process sales and manage transactions",
      icon: <ShoppingCart className="h-8 w-8 text-primary" />,
      link: "/pos",
      actions: [
        { label: "Open POS", link: "/pos" },
        { label: "View Transactions", link: "/transactions" },
      ],
    },
    {
      title: "Analytics",
      description: "View sales and inventory analytics",
      icon: <BarChart3 className="h-8 w-8 text-primary" />,
      link: "/dashboard",
      actions: [
        { label: "Dashboard", link: "/dashboard" },
        { label: "Sales Reports", link: "/reports/sales" },
      ],
    },
  ];

  return (
    <MainLayout>
      <PageHeader
        title="Inventory Management"
        description="Manage your products, stock, and inventory operations"
      />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {inventoryFeatures.map((feature, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3">
                {feature.icon}
                <div>
                  <CardTitle>{feature.title}</CardTitle>
                  <CardDescription>{feature.description}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="space-y-2">
                {feature.actions.map((action, actionIndex) => (
                  <div key={actionIndex} className="flex items-center">
                    <ArrowRightCircle className="h-4 w-4 mr-2 text-muted-foreground" />
                    <Link href={action.link} className="text-sm hover:underline">
                      {action.label}
                    </Link>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild className="w-full">
                <Link href={feature.link}>Go to {feature.title}</Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </MainLayout>
  );
}
