import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Purchase order update schema for validation
const purchaseOrderUpdateSchema = z.object({
  status: z.enum(['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'ORDERED', 'RECEIVED', 'CANCELLED']).optional(),
  notes: z.string().optional(),
  approvalNotes: z.string().optional(),
});

// GET /api/purchase-orders/[id] - Get purchase order details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        supplier: true,
        createdBy: {
          select: { id: true, name: true, email: true },
        },
        approvedBy: {
          select: { id: true, name: true, email: true },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    return NextResponse.json(purchaseOrder);
  } catch (error) {
    console.error('Error fetching purchase order:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/purchase-orders/[id] - Update purchase order
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = purchaseOrderUpdateSchema.parse(body);

    // Check if purchase order exists
    const existingPO = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: { supplier: true },
    });

    if (!existingPO) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
    };

    // If approving, set approval fields
    if (validatedData.status === 'APPROVED' && existingPO.status !== 'APPROVED') {
      updateData.approvedById = auth.user.id;
      updateData.approvedAt = new Date();
    }

    // If receiving, set received date and update stock
    if (validatedData.status === 'RECEIVED' && existingPO.status !== 'RECEIVED') {
      updateData.receivedAt = new Date();
    }

    // Use transaction to ensure data consistency
    const updatedPO = await prisma.$transaction(async (tx) => {
      // Update purchase order
      const po = await tx.purchaseOrder.update({
        where: { id },
        data: updateData,
        include: {
          supplier: true,
          createdBy: {
            select: { id: true, name: true, email: true },
          },
          approvedBy: {
            select: { id: true, name: true, email: true },
          },
          items: {
            include: {
              product: {
                include: {
                  category: true,
                  unit: true,
                },
              },
            },
          },
        },
      });

      // If status changed to RECEIVED, update warehouse stock
      if (validatedData.status === 'RECEIVED' && existingPO.status !== 'RECEIVED') {
        for (const item of po.items) {
          // Get or create warehouse stock
          const warehouseStock = await tx.warehouseStock.upsert({
            where: { productId: item.productId },
            update: {
              quantity: {
                increment: item.quantity,
              },
              lastUpdated: new Date(),
            },
            create: {
              productId: item.productId,
              quantity: item.quantity,
              minThreshold: 0,
              lastUpdated: new Date(),
            },
          });

          // Create stock history entry
          await tx.stockHistory.create({
            data: {
              productId: item.productId,
              warehouseStockId: warehouseStock.id,
              previousQuantity: Number(warehouseStock.quantity) - Number(item.quantity),
              newQuantity: Number(warehouseStock.quantity),
              changeQuantity: Number(item.quantity),
              source: "PURCHASE",
              referenceId: po.id,
              referenceType: "PurchaseOrder",
              notes: `Purchase order received: ${po.id.slice(-8).toUpperCase()}`,
              userId: auth.user.id,
            },
          });
        }
      }

      return po;
    });

    // Log activity
    let activityDetails = `Updated purchase order for supplier: ${existingPO.supplier.name}`;
    if (validatedData.status) {
      activityDetails += ` - Status changed to: ${validatedData.status}`;
    }

    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: 'UPDATE_PURCHASE_ORDER',
        details: activityDetails,
      },
    });

    return NextResponse.json(updatedPO);
  } catch (error) {
    console.error('Error updating purchase order:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', issues: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
