"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Loader2, Check, X, Package, Edit, Printer } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

interface PurchaseOrder {
  id: string;
  orderDate: string;
  status: string;
  subtotal: number;
  tax: number;
  taxPercentage?: number;
  total: number;
  notes?: string;
  createdAt: string;
  approvedAt?: string;
  receivedAt?: string;
  supplier: {
    id: string;
    name: string;
    contactPerson?: string;
    phone?: string;
    email?: string;
  };
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  approvedBy?: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    quantity: number;
    unitPrice: number;
    subtotal: number;
    product: {
      id: string;
      name: string;
      sku: string;
      category?: {
        name: string;
      };
      unit?: {
        name: string;
      };
    };
  }>;
}

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  PENDING_APPROVAL: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  ORDERED: "bg-purple-100 text-purple-800",
  RECEIVED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
};

const statusLabels = {
  DRAFT: "Draft",
  PENDING_APPROVAL: "Pending Approval",
  APPROVED: "Approved",
  ORDERED: "Ordered",
  RECEIVED: "Received",
  CANCELLED: "Cancelled",
};

export default function PurchaseOrderDetailPage() {
  const params = useParams();
  const id = params.id as string;

  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPurchaseOrder = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/purchase-orders/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Purchase order not found");
        }
        throw new Error("Failed to fetch purchase order");
      }

      const data = await response.json();
      setPurchaseOrder(data);
    } catch (error) {
      console.error("Error fetching purchase order:", error);
      setError(error instanceof Error ? error.message : "Failed to load purchase order");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchPurchaseOrder();
    }
  }, [id]);

  const updateStatus = async (newStatus: string, actionLabel: string) => {
    if (!purchaseOrder) return;

    try {
      setUpdating(true);

      const response = await fetch(`/api/purchase-orders/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${actionLabel.toLowerCase()}`);
      }

      const updatedPO = await response.json();
      setPurchaseOrder(updatedPO);
      toast.success(`Purchase order ${actionLabel.toLowerCase()} successfully`);
    } catch (error) {
      console.error(`Error ${actionLabel.toLowerCase()} purchase order:`, error);
      toast.error(`Failed to ${actionLabel.toLowerCase()} purchase order`);
    } finally {
      setUpdating(false);
    }
  };

  const handlePrint = async () => {
    if (!purchaseOrder) return;

    try {
      const response = await fetch(`/api/purchase-orders/${id}/print`);
      if (!response.ok) {
        throw new Error("Failed to generate PDF");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `purchase-order-${purchaseOrder.id.slice(-8).toUpperCase()}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Purchase order PDF downloaded successfully");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF");
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading purchase order...</span>
        </div>
      </MainLayout>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <MainLayout>
        <div className="text-center p-12">
          <h3 className="text-lg font-medium mb-2">Purchase Order Not Found</h3>
          <p className="text-muted-foreground mb-4">
            {error || "The purchase order you're looking for doesn't exist."}
          </p>
          <Button asChild>
            <Link href="/inventory/purchase-orders">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Purchase Orders
            </Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  const canApprove =
    purchaseOrder.status === "DRAFT" || purchaseOrder.status === "PENDING_APPROVAL";
  const canReceive = purchaseOrder.status === "APPROVED" || purchaseOrder.status === "ORDERED";
  const canCancel = !["RECEIVED", "CANCELLED"].includes(purchaseOrder.status);

  return (
    <MainLayout>
      <PageHeader
        title={`Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()}`}
        description={`Purchase order for ${purchaseOrder.supplier.name}`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/inventory/purchase-orders">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Purchase Orders
              </Link>
            </Button>

            <Button variant="outline" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Print PDF
            </Button>

            {canApprove && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button disabled={updating}>
                    <Check className="h-4 w-4 mr-2" />
                    Approve
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Approve Purchase Order</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to approve this purchase order? This action cannot be
                      undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => updateStatus("APPROVED", "Approved")}>
                      Approve
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {canReceive && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" disabled={updating}>
                    <Package className="h-4 w-4 mr-2" />
                    Mark as Received
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Mark as Received</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to mark this purchase order as received? This will
                      update inventory levels.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => updateStatus("RECEIVED", "Received")}>
                      Mark as Received
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {canCancel && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" disabled={updating}>
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Cancel Purchase Order</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to cancel this purchase order? This action cannot be
                      undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>No, Keep Order</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => updateStatus("CANCELLED", "Cancelled")}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Yes, Cancel Order
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        }
      />

      <div className="space-y-6">
        {/* Purchase Order Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Order Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Status</label>
                <div className="mt-1">
                  <Badge
                    className={statusColors[purchaseOrder.status as keyof typeof statusColors]}
                  >
                    {statusLabels[purchaseOrder.status as keyof typeof statusLabels]}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Order Date</label>
                <p className="mt-1">{new Date(purchaseOrder.orderDate).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Created By</label>
                <p className="mt-1">{purchaseOrder.createdBy.name}</p>
              </div>
              {purchaseOrder.approvedBy && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Approved By</label>
                  <p className="mt-1">{purchaseOrder.approvedBy.name}</p>
                </div>
              )}
              {purchaseOrder.approvedAt && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Approved At</label>
                  <p className="mt-1">{new Date(purchaseOrder.approvedAt).toLocaleString()}</p>
                </div>
              )}
              {purchaseOrder.receivedAt && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Received At</label>
                  <p className="mt-1">{new Date(purchaseOrder.receivedAt).toLocaleString()}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Supplier Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Supplier Name</label>
                <p className="mt-1 font-medium">{purchaseOrder.supplier.name}</p>
              </div>
              {purchaseOrder.supplier.contactPerson && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Contact Person
                  </label>
                  <p className="mt-1">{purchaseOrder.supplier.contactPerson}</p>
                </div>
              )}
              {purchaseOrder.supplier.phone && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Phone</label>
                  <p className="mt-1">{purchaseOrder.supplier.phone}</p>
                </div>
              )}
              {purchaseOrder.supplier.email && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email</label>
                  <p className="mt-1">{purchaseOrder.supplier.email}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Items */}
        <Card>
          <CardHeader>
            <CardTitle>Items ({purchaseOrder.items.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead className="text-right">Unit Price</TableHead>
                  <TableHead className="text-right">Subtotal</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {purchaseOrder.items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.product.name}</TableCell>
                    <TableCell>{item.product.sku}</TableCell>
                    <TableCell>{item.product.category?.name || "-"}</TableCell>
                    <TableCell>{item.product.unit?.name || "-"}</TableCell>
                    <TableCell className="text-right">{item.quantity}</TableCell>
                    <TableCell className="text-right">
                      Rp {Number(item.unitPrice).toLocaleString("id-ID")}
                    </TableCell>
                    <TableCell className="text-right">
                      Rp {Number(item.subtotal).toLocaleString("id-ID")}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Totals */}
            <div className="flex justify-end mt-6">
              <div className="w-64 space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>Rp {Number(purchaseOrder.subtotal).toLocaleString("id-ID")}</span>
                </div>
                <div className="flex justify-between">
                  <span>
                    Tax {purchaseOrder.taxPercentage ? `(${purchaseOrder.taxPercentage}%)` : ""}:
                  </span>
                  <span>Rp {Number(purchaseOrder.tax).toLocaleString("id-ID")}</span>
                </div>
                <div className="flex justify-between font-bold border-t pt-2">
                  <span>Total:</span>
                  <span>Rp {Number(purchaseOrder.total).toLocaleString("id-ID")}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notes */}
        {purchaseOrder.notes && (
          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{purchaseOrder.notes}</p>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}
